"""
Fintech-grade secure token management service
Provides cryptographically secure tokens with proper expiration and invalidation
"""

import secrets
import time
from typing import Optional, Dict, Any, Tuple, TypeVar
from datetime import timed<PERSON>ta
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractBaseUser
from django.db import transaction
from django.http import HttpRequest
from .models import SecureToken
from .utils import (
    log_security_event,
    get_client_ip,
    generate_device_fingerprint,
    hash_token,
)
from .config import oauth2_security_config
from agritram.logger_utils import (
    generate_unique_request_id,
    log_operation_info,
    log_database_operation,
    log_security_event_standardized,
    log_business_event,
    log_performance_metric,
    create_logging_context,
)

User = get_user_model()

UserType = TypeVar("UserType", bound=AbstractBaseUser)


class SecureTokenService:
    """
    Service for managing secure tokens with fintech-grade security
    """

    # Use centralized configuration
    TOKEN_EXPIRATION = oauth2_security_config.TOKEN_EXPIRATION
    MAX_ATTEMPTS = oauth2_security_config.TOKEN_MAX_ATTEMPTS

    @classmethod
    def generate_token(
        cls,
        user: UserType,
        token_type: str,
        request: HttpRequest,
        metadata: Optional[Dict[str, Any]] = None,
        unique_id: Optional[str] = None,
    ) -> Tuple[str, SecureToken]:
        """
        Generate a new secure token and invalidate any existing active tokens of the same type

        Args:
            user: User for whom to generate the token
            token_type: Type of token ('activation', 'password_reset', etc.)
            request: HTTP request object for security context
            metadata: Additional metadata to store with the token

        Returns:
            Tuple of (raw_token, token_object)
        """
        if token_type not in cls.TOKEN_EXPIRATION:
            raise ValueError(f"Invalid token type: {token_type}")

        # Generate cryptographically secure token
        raw_token = secrets.token_urlsafe(32)
        token_hash = hash_token(raw_token)  # Use centralized hash_token function

        # Get security context
        ip_address = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")
        device_fingerprint = generate_device_fingerprint(request)

        with transaction.atomic():
            # Invalidate any existing active tokens of the same type
            cls._invalidate_existing_tokens(user, token_type, "new_token_requested")

            # Create new token
            expires_at = timezone.now() + timedelta(
                hours=cls.TOKEN_EXPIRATION[token_type]
            )

            token_obj = SecureToken.objects.create(
                user=user,
                token_type=token_type,
                token_hash=token_hash,
                ip_address=ip_address,
                user_agent=user_agent,
                device_fingerprint=device_fingerprint,
                expires_at=expires_at,
                max_attempts=cls.MAX_ATTEMPTS.get(token_type, 3),
                metadata=metadata or {},
            )

            # Log security event
            log_security_event(
                user=user,
                event_type=f"{token_type}_token_generated",
                description=f"New {token_type} token generated",
                ip_address=ip_address,
                user_agent=user_agent,
                metadata={
                    "token_id": str(token_obj.id),
                    "expires_at": expires_at.isoformat(),
                    "device_fingerprint": device_fingerprint,
                },
            )

        return raw_token, token_obj

    @classmethod
    def validate_token(
        cls,
        raw_token: str,
        token_type: str,
        request: HttpRequest,
        user: Optional[UserType] = None,
    ) -> Tuple[bool, Optional[SecureToken], str]:
        """
        Validate a token and return validation result

        Args:
            raw_token: The raw token string
            token_type: Expected token type
            request: HTTP request object for security context
            user: Optional user to validate against

        Returns:
            Tuple of (is_valid, token_object, error_message)
        """
        try:
            token_hash = hash_token(raw_token)  # Use centralized hash_token function

            # Find token
            query = SecureToken.objects.filter(
                token_hash=token_hash, token_type=token_type
            )

            if user:
                query = query.filter(user=user)

            token_obj = query.first()

            if not token_obj:
                log_operation_info(
                    level="WARNING",
                    message=f"Token not found: {token_type} token validation failed",
                )
                return False, None, "Invalid or expired token"

            # Increment attempt counter
            token_obj.increment_attempt()

            # Validate token
            if not token_obj.is_valid():
                error_msg = cls._get_token_error_message(token_obj)
                unique_id = generate_unique_request_id()
                log_operation_info(
                    unique_id,
                    "TOKEN_VALIDATION_FAILED",
                    f"Token validation failed for user {token_obj.user.email}: {error_msg}",
                    level="WARNING",
                )

                # Log security event
                log_security_event(
                    user=token_obj.user,
                    event_type="invalid_token_usage",
                    description=f"Invalid {token_type} token usage attempt",
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    metadata={
                        "token_id": str(token_obj.id),
                        "error": error_msg,
                        "attempt_count": token_obj.attempt_count,
                    },
                )

                return False, token_obj, error_msg

            # Additional security checks
            security_check_result = cls._perform_security_checks(token_obj, request)
            if not security_check_result[0]:
                return False, token_obj, security_check_result[1]

            log_operation_info(
                level="INFO",
                message=f"Token validated successfully for user {token_obj.user.email}",
            )
            return True, token_obj, "Token is valid"

        except Exception as e:
            log_operation_info(
                level="ERROR", message=f"Token validation error: {str(e)}"
            )
            return False, None, "Token validation failed"

    @classmethod
    def use_token(cls, token_obj: SecureToken, request: HttpRequest) -> bool:
        """
        Mark a token as used after successful validation

        Args:
            token_obj: The token object to mark as used
            request: HTTP request object for security context

        Returns:
            True if token was successfully marked as used
        """
        try:
            with transaction.atomic():
                token_obj.mark_used()

                # Log security event
                log_security_event(
                    user=token_obj.user,
                    event_type=f"{token_obj.token_type}_token_used",
                    description=f"{token_obj.token_type.title()} token used successfully",
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    metadata={
                        "token_id": str(token_obj.id),
                        "used_at": (
                            token_obj.used_at.isoformat() if token_obj.used_at else None
                        ),
                    },
                )
                return True

        except Exception as e:
            log_operation_info(
                level="ERROR", message=f"Error marking token as used: {str(e)}"
            )
            return False

    @classmethod
    def cleanup_expired_tokens(cls, days_to_keep: int = 30) -> Dict[str, int]:
        """
        Clean up expired and old tokens

        Args:
            days_to_keep: Number of days to keep expired tokens for audit purposes

        Returns:
            Dictionary with cleanup statistics
        """
        try:
            cutoff_date = timezone.now() - timedelta(days=days_to_keep)

            # Count tokens to be deleted
            expired_tokens = SecureToken.objects.filter(
                expires_at__lt=timezone.now(), created_at__lt=cutoff_date
            )

            old_used_tokens = SecureToken.objects.filter(
                status="used", used_at__lt=cutoff_date
            )

            old_invalidated_tokens = SecureToken.objects.filter(
                status="invalidated", invalidated_at__lt=cutoff_date
            )

            expired_count = expired_tokens.count()
            used_count = old_used_tokens.count()
            invalidated_count = old_invalidated_tokens.count()

            # Delete tokens
            expired_tokens.delete()
            old_used_tokens.delete()
            old_invalidated_tokens.delete()

            total_cleaned = expired_count + used_count + invalidated_count

            log_operation_info(
                level="INFO", message=f"Cleaned up {total_cleaned} old tokens"
            )

            return {
                "expired_tokens_deleted": expired_count,
                "used_tokens_deleted": used_count,
                "invalidated_tokens_deleted": invalidated_count,
                "total_deleted": total_cleaned,
            }

        except Exception as e:
            log_operation_info(
                level="ERROR", message=f"Error during token cleanup: {str(e)}"
            )
            return {"error": str(e)}

    @classmethod
    def _invalidate_existing_tokens(cls, user: UserType, token_type: str, reason: str):
        """Invalidate existing active tokens of the same type"""
        existing_tokens = SecureToken.objects.filter(
            user=user, token_type=token_type, status="active"
        )

        for token in existing_tokens:
            token.invalidate(reason)
            log_operation_info(
                level="INFO",
                message=f"Invalidated existing {token_type} token for user {user.email}",
            )

    @classmethod
    def _get_token_error_message(cls, token_obj: SecureToken) -> str:
        """Get appropriate error message based on token status"""
        if token_obj.status == "expired":
            return "Token has expired"
        elif token_obj.status == "used":
            return "Token has already been used"
        elif token_obj.status == "invalidated":
            return "Token has been invalidated"
        elif token_obj.attempt_count >= token_obj.max_attempts:
            return "Too many failed attempts"
        else:
            return "Token is invalid"

    @classmethod
    def _perform_security_checks(
        cls, token_obj: SecureToken, request: HttpRequest
    ) -> Tuple[bool, str]:
        """Perform additional security checks on token usage"""
        # Check if IP address has changed significantly (optional - can be configured)
        current_ip = get_client_ip(request)

        # For now, we'll log IP changes but not block them
        # In a stricter implementation, you might want to block cross-IP usage
        if token_obj.ip_address != current_ip:

            # Log security event but don't block
            log_security_event(
                user=token_obj.user,
                event_type="token_ip_change",
                description="Token used from different IP address",
                ip_address=current_ip,
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                metadata={
                    "token_id": str(token_obj.id),
                    "original_ip": token_obj.ip_address,
                    "current_ip": current_ip,
                },
            )

        return True, "Security checks passed"


# Global service instance
secure_token_service = SecureTokenService()
